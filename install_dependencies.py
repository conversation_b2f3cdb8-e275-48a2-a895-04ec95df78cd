#!/usr/bin/env python3
"""
安装改进的GAN训练所需的依赖项
"""

import subprocess
import sys
import importlib

def install_package(package_name, import_name=None):
    """安装Python包"""
    if import_name is None:
        import_name = package_name
    
    try:
        importlib.import_module(import_name)
        print(f"✓ {package_name} 已安装")
        return True
    except ImportError:
        print(f"× {package_name} 未安装，正在安装...")
        try:
            subprocess.check_call([sys.executable, "-m", "pip", "install", package_name])
            print(f"✓ {package_name} 安装成功")
            return True
        except subprocess.CalledProcessError:
            print(f"✗ {package_name} 安装失败")
            return False

def main():
    """主函数"""
    print("检查并安装改进的GAN训练所需依赖...")
    print("=" * 50)
    
    # 需要安装的包列表
    packages = [
        ("torch", "torch"),
        ("torchvision", "torchvision"),
        ("numpy", "numpy"),
        ("matplotlib", "matplotlib"),
        ("scikit-image", "skimage"),
        ("pytorch-msssim", "pytorch_msssim"),
        ("lpips", "lpips"),  # 新增的LPIPS包
        ("rasterio", "rasterio"),
        ("pillow", "PIL")
    ]
    
    failed_packages = []
    
    for package_name, import_name in packages:
        if not install_package(package_name, import_name):
            failed_packages.append(package_name)
    
    print("\n" + "=" * 50)
    
    if failed_packages:
        print(f"✗ 以下包安装失败: {', '.join(failed_packages)}")
        print("请手动安装这些包或检查网络连接。")
        return False
    else:
        print("✓ 所有依赖项安装完成！")
        print("\n可以运行以下命令测试改进的指标:")
        print("python test_improved_metrics.py")
        return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
