import torch
import torch.nn.functional as F
from skimage.metrics import peak_signal_noise_ratio as psnr
import numpy as np
from pytorch_msssim import ssim  # Using pytorch_msssim for SSIM
import lpips  # 添加LPIPS支持

class ImageMetrics:
    def __init__(self, device):
        self.device = device
        # 初始化LPIPS模型 (使用VGG网络)
        self.lpips_vgg = lpips.LPIPS(net='vgg').to(device)
        self.lpips_vgg.eval()  # 设置为评估模式

    def calculate_psnr(self, pred, target):
        """Calculate PSNR between predicted and target images."""
        pred = pred.detach().cpu().numpy()
        target = target.detach().cpu().numpy()

        # 计算数据的实际范围，而不是强制clamp到[0,1]
        data_min = min(pred.min(), target.min())
        data_max = max(pred.max(), target.max())
        data_range = data_max - data_min

        # 如果数据范围太小，使用默认范围
        if data_range < 1e-6:
            data_range = 1.0

        psnr_vals = []
        for p, t in zip(pred, target):
            # 使用实际数据范围计算PSNR
            psnr_val = psnr(t, p, data_range=data_range)
            psnr_vals.append(psnr_val)

        return np.mean(psnr_vals)

    def calculate_ssim(self, pred, target):
        """Calculate SSIM between predicted and target images."""
        pred = pred.detach().cpu()
        target = target.detach().cpu()

        # 计算数据的实际范围
        data_min = min(pred.min(), target.min())
        data_max = max(pred.max(), target.max())
        data_range = data_max - data_min

        # 如果数据范围太小，使用默认范围
        if data_range < 1e-6:
            data_range = 1.0

        # 将数据归一化到[0,1]范围用于SSIM计算
        pred_norm = (pred - data_min) / data_range
        target_norm = (target - data_min) / data_range

        ssim_vals = []
        for p, t in zip(pred_norm, target_norm):
            # Ensure the images are on the same device
            p = p.unsqueeze(0).to(self.device)
            t = t.unsqueeze(0).to(self.device)

            # Calculate SSIM using pytorch_msssim with normalized data
            ssim_val = ssim(p, t, data_range=1.0, size_average=True)
            ssim_vals.append(ssim_val.item())

        return np.mean(ssim_vals)

    def calculate_lpips(self, pred, target):
        """Calculate LPIPS between predicted and target images."""
        pred = pred.detach()
        target = target.detach()

        # 计算数据的实际范围并归一化到[-1, 1]
        data_min = min(pred.min(), target.min())
        data_max = max(pred.max(), target.max())
        data_range = data_max - data_min

        if data_range < 1e-6:
            data_range = 1.0

        # 归一化到[-1, 1]范围 (LPIPS期望的输入范围)
        pred_norm = 2.0 * (pred - data_min) / data_range - 1.0
        target_norm = 2.0 * (target - data_min) / data_range - 1.0

        # 确保输入是3通道的 (LPIPS要求RGB输入)
        if pred_norm.size(1) == 1:
            pred_norm = pred_norm.repeat(1, 3, 1, 1)
        if target_norm.size(1) == 1:
            target_norm = target_norm.repeat(1, 3, 1, 1)

        # 移动到正确的设备
        pred_norm = pred_norm.to(self.device)
        target_norm = target_norm.to(self.device)

        # 计算LPIPS
        with torch.no_grad():
            lpips_val = self.lpips_vgg(pred_norm, target_norm)

        return lpips_val.mean().item()

    def calculate_metrics(self, pred, target):
        """Calculate image metrics: PSNR, SSIM, LPIPS."""
        psnr_val = self.calculate_psnr(pred, target)
        ssim_val = self.calculate_ssim(pred, target)
        lpips_val = self.calculate_lpips(pred, target)
        return psnr_val, ssim_val, lpips_val
