import torch
import torch.nn.functional as F
from skimage.metrics import peak_signal_noise_ratio as psnr
import numpy as np
from pytorch_msssim import ssim  # Using pytorch_msssim for SSIM

class ImageMetrics:
    def __init__(self, device):
        self.device = device

    def calculate_psnr(self, pred, target):
        """Calculate PSNR between predicted and target images."""
        pred = pred.detach().cpu().numpy()
        target = target.detach().cpu().numpy()

        # 计算数据的实际范围，而不是强制clamp到[0,1]
        data_min = min(pred.min(), target.min())
        data_max = max(pred.max(), target.max())
        data_range = data_max - data_min

        # 如果数据范围太小，使用默认范围
        if data_range < 1e-6:
            data_range = 1.0

        psnr_vals = []
        for p, t in zip(pred, target):
            # 使用实际数据范围计算PSNR
            psnr_val = psnr(t, p, data_range=data_range)
            psnr_vals.append(psnr_val)

        return np.mean(psnr_vals)

    def calculate_ssim(self, pred, target):
        """Calculate SSIM between predicted and target images."""
        pred = pred.detach().cpu()
        target = target.detach().cpu()

        # 计算数据的实际范围
        data_min = min(pred.min(), target.min())
        data_max = max(pred.max(), target.max())
        data_range = data_max - data_min

        # 如果数据范围太小，使用默认范围
        if data_range < 1e-6:
            data_range = 1.0

        # 将数据归一化到[0,1]范围用于SSIM计算
        pred_norm = (pred - data_min) / data_range
        target_norm = (target - data_min) / data_range

        ssim_vals = []
        for p, t in zip(pred_norm, target_norm):
            # Ensure the images are on the same device
            p = p.unsqueeze(0).to(self.device)
            t = t.unsqueeze(0).to(self.device)

            # Calculate SSIM using pytorch_msssim with normalized data
            ssim_val = ssim(p, t, data_range=1.0, size_average=True)
            ssim_vals.append(ssim_val.item())

        return np.mean(ssim_vals)

    def calculate_metrics(self, pred, target):
        """Calculate image metrics: PSNR, SSIM."""
        psnr_val = self.calculate_psnr(pred, target)
        ssim_val = self.calculate_ssim(pred, target)
        return psnr_val, ssim_val
