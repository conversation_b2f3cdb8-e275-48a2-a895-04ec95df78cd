#!/usr/bin/env python3
"""
测试改进的评估指标和模型保存策略
"""

import torch
import numpy as np
from utils.vloss import ImageMetrics
from train import compute_mixed_score

def test_improved_metrics():
    """测试改进的评估指标计算"""
    print("测试改进的评估指标...")
    
    # 检查CUDA可用性
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"使用设备: {device}")
    
    try:
        # 初始化指标计算器
        metrics = ImageMetrics(device)
        print("✓ ImageMetrics初始化成功")
        
        # 创建测试数据
        batch_size, channels, height, width = 2, 1, 64, 64
        pred = torch.randn(batch_size, channels, height, width).to(device)
        target = torch.randn(batch_size, channels, height, width).to(device)
        
        # 测试指标计算
        psnr_val, ssim_val, lpips_val = metrics.calculate_metrics(pred, target)
        print(f"✓ 指标计算成功:")
        print(f"  PSNR: {psnr_val:.4f} dB")
        print(f"  SSIM: {ssim_val:.4f}")
        print(f"  LPIPS: {lpips_val:.4f}")
        
        # 测试对抗分数 (模拟)
        adv_score = 0.7  # 模拟对抗分数
        
        # 测试混合评分计算
        mixed_score = compute_mixed_score(psnr_val, ssim_val, lpips_val, adv_score)
        print(f"✓ 混合评分计算成功: {mixed_score:.4f}")
        
        # 测试滑动窗口逻辑
        print("\n测试滑动窗口保存策略...")
        mixed_score_history = []
        window_size = 3
        improvement_threshold = 0.01
        best_moving_average = 0.0
        
        # 模拟几个epoch的评分
        test_scores = [0.65, 0.67, 0.69, 0.71, 0.68, 0.72, 0.74]
        
        for epoch, score in enumerate(test_scores):
            mixed_score_history.append(score)
            
            if len(mixed_score_history) >= window_size:
                current_moving_average = sum(mixed_score_history[-window_size:]) / window_size
                
                if current_moving_average > best_moving_average + improvement_threshold:
                    best_moving_average = current_moving_average
                    print(f"  Epoch {epoch+1}: 保存模型 (滑动平均: {current_moving_average:.4f}, "
                          f"改进: {current_moving_average - (best_moving_average - improvement_threshold):.4f})")
                else:
                    print(f"  Epoch {epoch+1}: 不保存 (滑动平均: {current_moving_average:.4f}, "
                          f"需要改进: {best_moving_average + improvement_threshold:.4f})")
            else:
                print(f"  Epoch {epoch+1}: 初期阶段，评分: {score:.4f}")
        
        print("\n✓ 所有测试通过！")
        return True
        
    except ImportError as e:
        print(f"✗ 导入错误: {e}")
        print("请安装LPIPS: pip install lpips")
        return False
    except Exception as e:
        print(f"✗ 测试失败: {e}")
        return False

def test_weights_sensitivity():
    """测试不同权重配置的敏感性"""
    print("\n测试权重配置敏感性...")
    
    # 模拟指标值
    psnr, ssim, lpips, adv_score = 35.0, 0.85, 0.15, 0.75
    
    # 测试不同权重配置
    weight_configs = [
        ([0.35, 0.25, 0.25, 0.15], "推荐配置"),
        ([0.4, 0.3, 0.2, 0.1], "更重视传统指标"),
        ([0.25, 0.25, 0.35, 0.15], "更重视感知质量"),
        ([0.3, 0.2, 0.2, 0.3], "更重视对抗性能")
    ]
    
    for weights, desc in weight_configs:
        score = compute_mixed_score(psnr, ssim, lpips, adv_score, weights)
        print(f"  {desc}: {score:.4f} (权重: {weights})")

if __name__ == "__main__":
    print("=" * 60)
    print("改进的GAN训练评估指标测试")
    print("=" * 60)
    
    success = test_improved_metrics()
    
    if success:
        test_weights_sensitivity()
        print("\n" + "=" * 60)
        print("测试完成！可以开始使用改进的训练脚本。")
        print("=" * 60)
    else:
        print("\n" + "=" * 60)
        print("测试失败！请检查依赖项安装。")
        print("=" * 60)
