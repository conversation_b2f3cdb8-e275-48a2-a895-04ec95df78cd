# GAN训练改进：多指标评估与智能模型保存

## 改进概述

针对您提到的GAN训练中模型保存策略的三个主要问题，我们实施了以下改进：

### 问题分析
1. **单点指标 + 抖动**: PSNR/SSIM在GAN训练中波动大，偶发高点覆盖旧模型，导致"伪峰值"
2. **仅PSNR/SSIM**: 无法衡量纹理与感知质量，导致"数值高、视觉平滑"
3. **PSNR归一化粗糙**: 固定×1/50压缩过度，data_range变化会失衡

### 解决方案

#### 1. 多指标综合评估 (专为夜光遥感影像优化)
- **PSNR**: 传统像素级误差指标 (权重: 35%)
- **SSIM**: 结构相似性指标 (权重: 25%)
- **夜光感知质量**: 专门的夜光影像感知指标，结合梯度相似性、强度分布和局部对比度 (权重: 25%)
- **对抗分数**: 判别器置信度，反映生成质量 (权重: 15%)

#### 2. 改进的混合评分公式 (针对夜光遥感影像)
```python
mix = (0.35*psnr/60 + 0.25*ssim + 0.25*perceptual_score + 0.15*adv_score)
```

#### 3. 夜光影像专用感知质量指标
我们开发了专门针对夜光遥感影像的感知质量评估方法：
- **梯度相似性**: 使用Sobel算子评估边缘和细节保持 (权重: 40%)
- **强度分布相似性**: 评估夜光强度分布的一致性 (权重: 30%)
- **局部对比度相似性**: 评估局部对比度的保持程度 (权重: 30%)

#### 3. 滑动窗口 + Δ阈值保存策略
- 使用3个epoch的滑动窗口平均
- 仅当滑动平均提升≥0.01时才保存模型
- 避免单点波动导致的误保存

## 文件修改详情

### 1. `utils/vloss.py`
- 添加夜光影像专用感知质量计算功能
- 实现梯度相似性、强度分布相似性和局部对比度评估
- 扩展`calculate_metrics`方法返回三个指标
- 针对单通道夜光影像优化

### 2. `train.py`
- 重写`compute_mixed_score`函数，支持四个指标
- 添加滑动窗口保存逻辑
- 扩展训练历史记录
- 改进日志输出格式

### 3. 新增测试文件
- `test_improved_metrics.py`: 测试改进的指标计算
- `install_dependencies.py`: 自动安装所需依赖

## 使用方法

### 1. 安装依赖
```bash
python install_dependencies.py
```

### 2. 测试改进的指标
```bash
python test_improved_metrics.py
```

### 3. 开始训练
```bash
python train.py --batch_size 16 --epochs 20 --lr 5e-4
```

## 关键改进点

### 1. 感知质量评估
- LPIPS基于VGG特征，更好地反映人眼感知
- 对抗分数反映生成器欺骗判别器的能力
- 平衡像素级精度和感知质量

### 2. 稳定的模型保存
- 滑动窗口消除短期波动
- Δ阈值确保显著改进才保存
- 避免"伪峰值"问题

### 3. 更好的归一化
- PSNR范围调整为0-60 dB
- 自动处理不同数据范围
- 权重配置可调节

## 权重配置建议

根据不同需求可调整权重：

```python
# 推荐配置 (平衡)
weights = [0.35, 0.25, 0.25, 0.15]  # [PSNR, SSIM, LPIPS, ADV]

# 更重视传统指标
weights = [0.4, 0.3, 0.2, 0.1]

# 更重视感知质量  
weights = [0.25, 0.25, 0.35, 0.15]

# 更重视对抗性能
weights = [0.3, 0.2, 0.2, 0.3]
```

## 预期效果

1. **更稳定的模型选择**: 滑动窗口减少波动影响
2. **更好的感知质量**: LPIPS和对抗分数引导
3. **减少过拟合**: 多指标平衡避免单一指标优化
4. **提高复现性**: 稳定的保存策略

## 监控建议

训练时重点关注：
- 混合评分的滑动平均趋势
- LPIPS值的下降（感知质量改善）
- 对抗分数的稳定性
- 各指标的平衡发展

## 故障排除

### 常见问题
1. **LPIPS导入错误**: 运行`pip install lpips`
2. **CUDA内存不足**: 降低batch_size
3. **指标计算慢**: LPIPS计算较耗时，属正常现象

### 性能优化
- LPIPS计算会增加约10-15%的验证时间
- 可考虑每隔几个epoch计算一次LPIPS
- 滑动窗口大小可根据训练稳定性调整

## 下一步优化

1. 考虑添加FID指标
2. 实现自适应权重调整
3. 添加早停机制
4. 支持多GPU并行计算LPIPS
