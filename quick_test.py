#!/usr/bin/env python3
"""
快速测试夜光影像专用感知质量指标
"""

import torch
import numpy as np

def test_nightlight_perceptual_score():
    """测试夜光影像感知质量计算"""
    print("测试夜光影像感知质量指标...")
    
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"使用设备: {device}")
    
    # 创建模拟的夜光影像数据
    batch_size, channels, height, width = 2, 1, 64, 64
    
    # 创建具有城市特征的模拟夜光数据
    pred = torch.zeros(batch_size, channels, height, width).to(device)
    target = torch.zeros(batch_size, channels, height, width).to(device)
    
    # 添加一些城市光源模式
    for b in range(batch_size):
        # 中心城市区域
        pred[b, 0, 20:40, 20:40] = torch.rand(20, 20) * 0.8 + 0.2
        target[b, 0, 20:40, 20:40] = torch.rand(20, 20) * 0.8 + 0.2
        
        # 一些道路模式
        pred[b, 0, 30, :] = torch.rand(width) * 0.5 + 0.1
        target[b, 0, 30, :] = torch.rand(width) * 0.5 + 0.1
    
    print(f"创建测试数据: {pred.shape}")
    print(f"数据范围: [{pred.min():.3f}, {pred.max():.3f}]")
    
    # 测试梯度计算
    def compute_gradients(img):
        sobel_x = torch.tensor([[-1, 0, 1], [-2, 0, 2], [-1, 0, 1]], 
                             dtype=torch.float32, device=img.device).view(1, 1, 3, 3)
        sobel_y = torch.tensor([[-1, -2, -1], [0, 0, 0], [1, 2, 1]], 
                             dtype=torch.float32, device=img.device).view(1, 1, 3, 3)
        
        grad_x = torch.nn.functional.conv2d(img, sobel_x, padding=1)
        grad_y = torch.nn.functional.conv2d(img, sobel_y, padding=1)
        gradient_magnitude = torch.sqrt(grad_x**2 + grad_y**2 + 1e-8)
        return gradient_magnitude
    
    pred_grad = compute_gradients(pred)
    target_grad = compute_gradients(target)
    print(f"✓ 梯度计算成功: {pred_grad.shape}")
    
    # 测试梯度相似性
    pred_grad_flat = pred_grad.view(pred_grad.size(0), -1)
    target_grad_flat = target_grad.view(target_grad.size(0), -1)
    gradient_similarity = torch.nn.functional.cosine_similarity(pred_grad_flat, target_grad_flat, dim=1).mean()
    print(f"✓ 梯度相似性: {gradient_similarity:.4f}")
    
    # 测试强度分布相似性
    pred_flat = pred.view(pred.size(0), -1)
    target_flat = target.view(target.size(0), -1)
    
    mean_diff = torch.abs(pred_flat.mean(dim=1) - target_flat.mean(dim=1)).mean()
    std_diff = torch.abs(pred_flat.std(dim=1) - target_flat.std(dim=1)).mean()
    
    mean_sim = torch.exp(-mean_diff)
    std_sim = torch.exp(-std_diff)
    intensity_similarity = (mean_sim + std_sim) / 2
    print(f"✓ 强度分布相似性: {intensity_similarity:.4f}")
    
    # 测试局部对比度
    def compute_local_contrast(img, kernel_size=5):
        padding = kernel_size // 2
        unfold = torch.nn.functional.unfold(img, kernel_size, padding=padding)
        local_std = unfold.std(dim=1, keepdim=True)
        local_std = torch.nn.functional.fold(local_std, img.shape[-2:], kernel_size, padding=padding)
        return local_std
    
    pred_contrast = compute_local_contrast(pred)
    target_contrast = compute_local_contrast(target)
    
    contrast_similarity = torch.nn.functional.cosine_similarity(
        pred_contrast.view(pred_contrast.size(0), -1),
        target_contrast.view(target_contrast.size(0), -1),
        dim=1
    ).mean()
    print(f"✓ 局部对比度相似性: {contrast_similarity:.4f}")
    
    # 综合感知质量分数
    perceptual_score = (0.4 * gradient_similarity + 
                       0.3 * intensity_similarity + 
                       0.3 * contrast_similarity)
    print(f"✓ 综合感知质量分数: {perceptual_score:.4f}")
    
    return True

def test_mixed_score():
    """测试混合评分计算"""
    print("\n测试混合评分计算...")
    
    # 模拟指标值
    psnr = 35.0
    ssim = 0.85
    perceptual_score = 0.82
    adv_score = 0.75
    
    # 计算混合评分
    weights = [0.35, 0.25, 0.25, 0.15]
    
    normalized_psnr = min(psnr / 60.0, 1.0)
    normalized_ssim = ssim
    normalized_perceptual = perceptual_score
    normalized_adv = adv_score
    
    mixed_score = (weights[0] * normalized_psnr + 
                   weights[1] * normalized_ssim +
                   weights[2] * normalized_perceptual +
                   weights[3] * normalized_adv)
    
    print(f"输入指标:")
    print(f"  PSNR: {psnr:.2f} dB -> 归一化: {normalized_psnr:.4f}")
    print(f"  SSIM: {ssim:.4f} -> 归一化: {normalized_ssim:.4f}")
    print(f"  感知质量: {perceptual_score:.4f} -> 归一化: {normalized_perceptual:.4f}")
    print(f"  对抗分数: {adv_score:.4f} -> 归一化: {normalized_adv:.4f}")
    print(f"✓ 混合评分: {mixed_score:.4f}")
    
    return True

if __name__ == "__main__":
    print("=" * 60)
    print("夜光影像专用感知质量指标快速测试")
    print("=" * 60)
    
    try:
        success1 = test_nightlight_perceptual_score()
        success2 = test_mixed_score()
        
        if success1 and success2:
            print("\n" + "=" * 60)
            print("✓ 所有测试通过！夜光影像专用指标工作正常。")
            print("可以开始使用改进的训练脚本。")
            print("=" * 60)
        else:
            print("\n" + "=" * 60)
            print("✗ 部分测试失败")
            print("=" * 60)
            
    except Exception as e:
        print(f"\n✗ 测试过程中出现错误: {e}")
        print("请检查PyTorch安装和CUDA配置")
